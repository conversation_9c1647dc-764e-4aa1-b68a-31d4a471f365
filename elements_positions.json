{"screenshot_info": {"width": 414, "height": 896, "description": "Mobile app file manager interface screenshot"}, "elements": [{"id": "status_bar", "type": "container", "description": "顶部状态栏", "x": 0, "y": 0, "width": 414, "height": 44, "background_color": "#FFFFFF"}, {"id": "time_display", "type": "text", "description": "时间显示 11:28", "x": 44, "y": 15, "width": 50, "height": 20, "text": "11:28", "color": "#000000"}, {"id": "signal_icon", "type": "icon", "description": "信号强度图标", "x": 320, "y": 18, "width": 20, "height": 12, "color": "#000000"}, {"id": "wifi_icon", "type": "icon", "description": "WiFi图标", "x": 350, "y": 18, "width": 15, "height": 12, "color": "#000000"}, {"id": "battery_icon", "type": "icon", "description": "电池图标", "x": 375, "y": 18, "width": 25, "height": 12, "color": "#000000"}, {"id": "header_container", "type": "container", "description": "标题栏容器", "x": 0, "y": 44, "width": 414, "height": 80, "background_color": "#FFFFFF"}, {"id": "home_title", "type": "text", "description": "Home标题", "x": 34, "y": 65, "width": 80, "height": 35, "text": "Home", "color": "#000000", "font_size": "28px", "font_weight": "bold"}, {"id": "blue_circle_button", "type": "button", "description": "蓝色圆形按钮", "x": 360, "y": 60, "width": 40, "height": 40, "background_color": "#4A90E2", "border_radius": "50%"}, {"id": "toolbar", "type": "container", "description": "工具栏", "x": 0, "y": 140, "width": 414, "height": 50, "background_color": "#F8F8F8"}, {"id": "select_button", "type": "button", "description": "Select按钮", "x": 34, "y": 155, "width": 60, "height": 20, "text": "Select", "color": "#999999"}, {"id": "kind_button", "type": "button", "description": "Kind按钮", "x": 350, "y": 155, "width": 40, "height": 20, "text": "Kind", "color": "#999999"}, {"id": "file_list", "type": "container", "description": "文件列表容器", "x": 0, "y": 200, "width": 414, "height": 500, "background_color": "#FFFFFF"}, {"id": "file_item_1", "type": "container", "description": "第一个文件项 - Flutter视频", "x": 20, "y": 220, "width": 374, "height": 80, "background_color": "#F8F8F8", "border_radius": "8px"}, {"id": "file_icon_1", "type": "icon", "description": "文件图标1 - 视频文件", "x": 50, "y": 240, "width": 40, "height": 40, "color": "#4A90E2"}, {"id": "file_name_1", "type": "text", "description": "文件名1", "x": 110, "y": 240, "width": 250, "height": 20, "text": "13-11 Flutter包大小优化_.MP4", "color": "#000000"}, {"id": "file_size_1", "type": "text", "description": "文件大小1", "x": 110, "y": 265, "width": 100, "height": 15, "text": "80.4 MB", "color": "#999999"}, {"id": "more_button_1", "type": "button", "description": "更多选项按钮1", "x": 370, "y": 245, "width": 20, "height": 30, "text": "⋮", "color": "#999999"}, {"id": "file_item_2", "type": "container", "description": "第二个文件项 - mov视频", "x": 20, "y": 320, "width": 374, "height": 80, "background_color": "#F8F8F8", "border_radius": "8px"}, {"id": "file_icon_2", "type": "icon", "description": "文件图标2 - 视频文件", "x": 50, "y": 340, "width": 40, "height": 40, "color": "#4A90E2"}, {"id": "file_name_2", "type": "text", "description": "文件名2", "x": 110, "y": 340, "width": 250, "height": 20, "text": "sd1748056775_2.mov", "color": "#000000"}, {"id": "file_size_2", "type": "text", "description": "文件大小2", "x": 110, "y": 365, "width": 100, "height": 15, "text": "34.2 MB", "color": "#999999"}, {"id": "more_button_2", "type": "button", "description": "更多选项按钮2", "x": 370, "y": 345, "width": 20, "height": 30, "text": "⋮", "color": "#999999"}, {"id": "file_item_3", "type": "container", "description": "第三个文件项 - JPEG图片", "x": 20, "y": 420, "width": 374, "height": 80, "background_color": "#F8F8F8", "border_radius": "8px"}, {"id": "file_icon_3", "type": "icon", "description": "文件图标3 - 图片文件", "x": 50, "y": 440, "width": 40, "height": 40, "color": "#4A90E2"}, {"id": "file_name_3", "type": "text", "description": "文件名3", "x": 110, "y": 440, "width": 250, "height": 20, "text": "IMG_0813.jpeg", "color": "#000000"}, {"id": "file_size_3", "type": "text", "description": "文件大小3", "x": 110, "y": 465, "width": 100, "height": 15, "text": "281 KB", "color": "#999999"}, {"id": "more_button_3", "type": "button", "description": "更多选项按钮3", "x": 370, "y": 445, "width": 20, "height": 30, "text": "⋮", "color": "#999999"}, {"id": "file_item_4", "type": "container", "description": "第四个文件项 - 图片文件夹", "x": 20, "y": 520, "width": 374, "height": 80, "background_color": "#F8F8F8", "border_radius": "8px"}, {"id": "file_icon_4", "type": "icon", "description": "文件图标4 - 文件夹", "x": 50, "y": 540, "width": 40, "height": 40, "color": "#4A90E2"}, {"id": "file_name_4", "type": "text", "description": "文件名4", "x": 110, "y": 540, "width": 250, "height": 20, "text": "图片", "color": "#000000"}, {"id": "file_size_4", "type": "text", "description": "文件大小4", "x": 110, "y": 565, "width": 100, "height": 15, "text": "Zero KB", "color": "#999999"}, {"id": "more_button_4", "type": "button", "description": "更多选项按钮4", "x": 370, "y": 545, "width": 20, "height": 30, "text": "⋮", "color": "#999999"}, {"id": "add_button", "type": "button", "description": "底部添加按钮", "x": 350, "y": 780, "width": 50, "height": 50, "background_color": "#4A90E2", "border_radius": "50%", "text": "+", "color": "#FFFFFF"}, {"id": "bottom_indicator", "type": "indicator", "description": "底部指示条", "x": 150, "y": 860, "width": 114, "height": 5, "background_color": "#000000", "border_radius": "3px"}]}