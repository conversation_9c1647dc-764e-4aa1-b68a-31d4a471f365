<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UI元素位置可视化工具</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .content {
            display: flex;
            gap: 20px;
            padding: 20px;
        }
        
        .canvas-container {
            flex: 1;
            position: relative;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            overflow: hidden;
            display: flex;
            justify-content: center;
            align-items: flex-start;
            background: #f8f8f8;
        }

        .canvas-wrapper {
            position: relative;
            max-width: 100%;
            max-height: 100%;
        }

        .canvas-container img {
            display: block;
            max-width: 100%;
            height: auto;
            border-radius: 6px;
        }

        #canvas {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: transparent;
            cursor: crosshair;
            z-index: 1;
        }
        
        .controls {
            width: 300px;
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
        }
        
        .control-group {
            margin-bottom: 20px;
        }
        
        .control-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }
        
        .checkbox-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        
        .checkbox-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .checkbox-item input[type="checkbox"] {
            margin: 0;
        }
        
        .element-info {
            background: white;
            border: 1px solid #ddd;
            border-radius: 6px;
            padding: 15px;
            margin-top: 20px;
            min-height: 100px;
        }
        
        .element-info h4 {
            margin: 0 0 10px 0;
            color: #333;
        }
        
        .element-info p {
            margin: 5px 0;
            font-size: 14px;
            color: #666;
        }
        
        button {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.3s;
        }
        
        button:hover {
            background: #5a6fd8;
        }
        
        .stats {
            background: #e8f4fd;
            border-left: 4px solid #2196F3;
            padding: 15px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📱 移动应用UI元素位置分析工具</h1>
            <p>基于截图分析的UI元素位置可视化</p>
        </div>
        
        <div class="content">
            <div class="canvas-container">
                <div class="canvas-wrapper">
                    <img src="./snapshots/cloud_drive_main.PNG" width="414" height="896" alt="移动应用截图">
                    <canvas id="canvas" width="414" height="896"></canvas>
                </div>
            </div>
            
            <div class="controls">
                <div class="stats">
                    <h4>📊 统计信息</h4>
                    <p>总元素数量: <span id="totalElements">0</span></p>
                    <p>画布尺寸: <span id="canvasSize">414 × 896</span></p>
                </div>
                
                <div class="control-group">
                    <label>🎨 显示选项</label>
                    <div class="checkbox-group">
                        <div class="checkbox-item">
                            <input type="checkbox" id="showBounds" checked>
                            <label for="showBounds">显示边界框</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="showLabels" checked>
                            <label for="showLabels">显示标签</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="showCoords" checked>
                            <label for="showCoords">显示坐标</label>
                        </div>
                    </div>
                </div>
                
                <div class="control-group">
                    <label>🔍 元素类型过滤</label>
                    <div class="checkbox-group" id="typeFilters">
                        <!-- 动态生成 -->
                    </div>
                </div>
                
                <div class="control-group">
                    <button onclick="exportData()">📥 导出数据</button>
                    <button onclick="resetView()" style="margin-top: 10px;">🔄 重置视图</button>
                </div>
                
                <div class="element-info">
                    <h4>🎯 元素信息</h4>
                    <p id="elementDetails">点击截图上的元素边界框查看详细信息</p>
                    <p style="font-size: 12px; color: #888; margin-top: 10px;">
                        💡 提示：canvas透明覆盖在截图上，显示元素边界框和标签
                    </p>
                </div>
            </div>
        </div>
    </div>

    <script>
        let elementsData = null;
        let canvas = null;
        let ctx = null;
        let selectedElement = null;
        
        // 初始化
        async function init() {
            canvas = document.getElementById('canvas');
            ctx = canvas.getContext('2d');
            
            // 加载元素数据
            try {
                const response = await fetch('elements_positions.json');
                elementsData = await response.json();
                
                document.getElementById('totalElements').textContent = elementsData.elements.length;
                document.getElementById('canvasSize').textContent = 
                    `${elementsData.screenshot_info.width} × ${elementsData.screenshot_info.height}`;
                
                setupTypeFilters();
                setupEventListeners();
                setupCanvasOverlay();
                drawElements();
            } catch (error) {
                console.error('加载数据失败:', error);
                document.getElementById('elementDetails').innerHTML = 
                    '<p style="color: red;">❌ 无法加载元素数据，请确保 elements_positions.json 文件存在</p>';
            }
        }

        // 设置canvas覆盖层
        function setupCanvasOverlay() {
            const img = document.querySelector('.canvas-container img');

            // 等待图片加载完成
            if (img.complete) {
                adjustCanvasSize();
            } else {
                img.addEventListener('load', adjustCanvasSize);
            }

            // 监听窗口大小变化
            window.addEventListener('resize', adjustCanvasSize);
        }

        // 调整canvas尺寸以匹配图片
        function adjustCanvasSize() {
            const img = document.querySelector('.canvas-container img');
            const rect = img.getBoundingClientRect();

            // 设置canvas的显示尺寸与图片一致
            canvas.style.width = rect.width + 'px';
            canvas.style.height = rect.height + 'px';
        }

        // 设置类型过滤器
        function setupTypeFilters() {
            const types = [...new Set(elementsData.elements.map(el => el.type))];
            const container = document.getElementById('typeFilters');
            
            types.forEach(type => {
                const div = document.createElement('div');
                div.className = 'checkbox-item';
                div.innerHTML = `
                    <input type="checkbox" id="type_${type}" checked onchange="drawElements()">
                    <label for="type_${type}">${type}</label>
                `;
                container.appendChild(div);
            });
        }
        
        // 设置事件监听器
        function setupEventListeners() {
            canvas.addEventListener('click', handleCanvasClick);
            
            ['showBounds', 'showLabels', 'showCoords'].forEach(id => {
                document.getElementById(id).addEventListener('change', drawElements);
            });
        }
        
        // 绘制所有元素
        function drawElements() {
            // 清除画布，保持透明背景
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            const showBounds = document.getElementById('showBounds').checked;
            const showLabels = document.getElementById('showLabels').checked;
            const showCoords = document.getElementById('showCoords').checked;
            
            // 获取启用的类型
            const enabledTypes = getEnabledTypes();
            
            elementsData.elements.forEach((element, index) => {
                if (!enabledTypes.includes(element.type)) return;
                
                const isSelected = selectedElement && selectedElement.id === element.id;
                
                // 绘制边界框
                if (showBounds) {
                    ctx.strokeStyle = isSelected ? '#ff4444' : getColorForType(element.type);
                    ctx.lineWidth = isSelected ? 3 : 2;
                    ctx.strokeRect(element.x, element.y, element.width, element.height);

                    // 填充半透明背景
                    ctx.fillStyle = isSelected ? 'rgba(255, 68, 68, 0.15)' : 'rgba(70, 144, 226, 0.08)';
                    ctx.fillRect(element.x, element.y, element.width, element.height);
                }

                // 绘制标签
                if (showLabels) {
                    const label = `${index + 1}. ${element.description}`;
                    const labelX = element.x + 2;
                    const labelY = element.y - 8;

                    // 绘制标签背景
                    ctx.font = '11px Arial';
                    const textMetrics = ctx.measureText(label);
                    const textWidth = textMetrics.width;

                    ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';
                    ctx.fillRect(labelX - 2, labelY - 12, textWidth + 4, 16);

                    // 绘制标签文字
                    ctx.fillStyle = '#ffffff';
                    ctx.fillText(label, labelX, labelY);
                }

                // 绘制坐标
                if (showCoords) {
                    const coords = `(${element.x}, ${element.y})`;
                    const coordX = element.x + 2;
                    const coordY = element.y + element.height - 2;

                    // 绘制坐标背景
                    ctx.font = '9px Arial';
                    const coordMetrics = ctx.measureText(coords);
                    const coordWidth = coordMetrics.width;

                    ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
                    ctx.fillRect(coordX - 1, coordY - 10, coordWidth + 2, 12);

                    // 绘制坐标文字
                    ctx.fillStyle = '#ffffff';
                    ctx.fillText(coords, coordX, coordY);
                }
            });
        }
        
        // 获取启用的类型
        function getEnabledTypes() {
            const types = [...new Set(elementsData.elements.map(el => el.type))];
            return types.filter(type => 
                document.getElementById(`type_${type}`).checked
            );
        }
        
        // 根据类型获取颜色
        function getColorForType(type) {
            const colors = {
                'container': '#4CAF50',
                'text': '#2196F3',
                'button': '#FF9800',
                'icon': '#9C27B0',
                'indicator': '#607D8B'
            };
            return colors[type] || '#666666';
        }
        
        // 处理画布点击
        function handleCanvasClick(event) {
            const rect = canvas.getBoundingClientRect();
            const x = (event.clientX - rect.left) * (canvas.width / rect.width);
            const y = (event.clientY - rect.top) * (canvas.height / rect.height);
            
            // 查找点击的元素
            const clickedElement = elementsData.elements.find(element => 
                x >= element.x && x <= element.x + element.width &&
                y >= element.y && y <= element.y + element.height
            );
            
            if (clickedElement) {
                selectedElement = clickedElement;
                showElementDetails(clickedElement);
                drawElements();
            }
        }
        
        // 显示元素详情
        function showElementDetails(element) {
            const details = document.getElementById('elementDetails');
            details.innerHTML = `
                <h4>🎯 ${element.description}</h4>
                <p><strong>ID:</strong> ${element.id}</p>
                <p><strong>类型:</strong> ${element.type}</p>
                <p><strong>位置:</strong> (${element.x}, ${element.y})</p>
                <p><strong>尺寸:</strong> ${element.width} × ${element.height}</p>
                ${element.text ? `<p><strong>文本:</strong> ${element.text}</p>` : ''}
                ${element.background_color ? `<p><strong>背景色:</strong> ${element.background_color}</p>` : ''}
                ${element.color ? `<p><strong>颜色:</strong> ${element.color}</p>` : ''}
            `;
        }
        
        // 导出数据
        function exportData() {
            const dataStr = JSON.stringify(elementsData, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = 'ui_elements_export.json';
            link.click();
            URL.revokeObjectURL(url);
        }
        
        // 重置视图
        function resetView() {
            selectedElement = null;
            document.getElementById('elementDetails').innerHTML = 
                '<h4>🎯 元素信息</h4><p>点击画布上的元素查看详细信息</p>';
            drawElements();
        }
        
        // 页面加载完成后初始化
        window.addEventListener('load', init);
    </script>
</body>
</html>
